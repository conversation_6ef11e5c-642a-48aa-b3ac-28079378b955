<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .test-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Mobile Safari Test</h1>
    <div class="test-box">
        <h2>Basic Functionality Test</h2>
        <p>If you can see this, HTML and CSS are working!</p>
        <button onclick="testJS()">Test JavaScript</button>
        <div id="js-result"></div>
    </div>
    
    <div class="test-box">
        <h2>Network Test</h2>
        <button onclick="testNetwork()">Test Network Connection</button>
        <div id="network-result"></div>
    </div>
    
    <div class="test-box">
        <h2>Module Test</h2>
        <button onclick="testModules()">Test ES Modules</button>
        <div id="module-result"></div>
    </div>

    <script>
        function testJS() {
            document.getElementById('js-result').innerHTML = '✅ JavaScript is working!';
        }
        
        function testNetwork() {
            fetch('/src/main.tsx')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('network-result').innerHTML = '✅ Can access main.tsx';
                    } else {
                        document.getElementById('network-result').innerHTML = '❌ Cannot access main.tsx: ' + response.status;
                    }
                })
                .catch(error => {
                    document.getElementById('network-result').innerHTML = '❌ Network error: ' + error.message;
                });
        }
        
        function testModules() {
            try {
                import('/src/main.tsx')
                    .then(() => {
                        document.getElementById('module-result').innerHTML = '✅ ES Modules working!';
                    })
                    .catch(error => {
                        document.getElementById('module-result').innerHTML = '❌ Module import failed: ' + error.message;
                    });
            } catch (error) {
                document.getElementById('module-result').innerHTML = '❌ Dynamic import not supported: ' + error.message;
            }
        }
    </script>
</body>
</html>
