<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta
      name="description"
      content="AI-powered photo conversations and medication reminders for memory care"
    />
    <meta name="theme-color" content="#4F46E5" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Mobile Safari specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="format-detection" content="telephone=no" />

    <title>Memory Companion - AI Photo Conversations & Care</title>

    <!-- Mobile Safari debugging -->
    <script>
      // Create mobile debug overlay
      let debugDiv = null;
      function createDebugOverlay() {
        if (debugDiv) return;
        debugDiv = document.createElement("div");
        debugDiv.id = "mobile-debug";
        debugDiv.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          max-height: 200px;
          background: rgba(0,0,0,0.8);
          color: white;
          font-size: 12px;
          padding: 10px;
          z-index: 9999;
          overflow-y: auto;
          display: none;
        `;
        document.body.appendChild(debugDiv);

        // Show debug on triple tap
        let tapCount = 0;
        document.addEventListener("touchstart", function () {
          tapCount++;
          setTimeout(() => (tapCount = 0), 500);
          if (tapCount === 3) {
            debugDiv.style.display =
              debugDiv.style.display === "none" ? "block" : "none";
          }
        });
      }

      function addDebugLog(message) {
        if (!debugDiv) createDebugOverlay();
        const time = new Date().toLocaleTimeString();
        debugDiv.innerHTML += `<div>[${time}] ${message}</div>`;
        debugDiv.scrollTop = debugDiv.scrollHeight;
      }

      // Mobile Safari error logging
      window.addEventListener("error", function (e) {
        const errorMsg = `ERROR: ${e.error?.message || e.message} at ${
          e.filename
        }:${e.lineno}`;
        console.error("Global error:", e.error, e.filename, e.lineno, e.colno);
        addDebugLog(errorMsg);
      });

      window.addEventListener("unhandledrejection", function (e) {
        const errorMsg = `PROMISE REJECTION: ${e.reason}`;
        console.error("Unhandled promise rejection:", e.reason);
        addDebugLog(errorMsg);
      });

      // Log when DOM is ready
      document.addEventListener("DOMContentLoaded", function () {
        console.log("DOM Content Loaded");
        addDebugLog("DOM Content Loaded");
      });

      // Log when page loads
      window.addEventListener("load", function () {
        console.log("Page fully loaded");
        addDebugLog("Page fully loaded");
      });

      addDebugLog("Debug script loaded - Triple tap to show/hide this overlay");
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Fallback for mobile Safari -->
    <noscript>
      <div style="padding: 20px; text-align: center">
        <h1>JavaScript Required</h1>
        <p>
          This app requires JavaScript to run. Please enable JavaScript in your
          browser settings.
        </p>
      </div>
    </noscript>
  </body>
</html>
